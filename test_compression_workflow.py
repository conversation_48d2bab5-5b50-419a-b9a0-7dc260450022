#!/usr/bin/env python3
"""
Test script to verify compression and archive creation workflow
"""

import os
import glob
import tarfile
from pathlib import Path
from src.tngd_backup.core.compression_service import CompressionService
from src.tngd_backup.core.config_manager import <PERSON>fi<PERSON><PERSON><PERSON><PERSON>

def test_compression_workflow():
    """Test the compression and archive creation workflow."""
    
    print("=== Testing Compression and Archive Creation Workflow ===")
    
    try:
        # Initialize components
        print("1. Initializing configuration and compression service...")
        config = ConfigManager('config/production.json')
        compression_service = CompressionService(config)
        
        # Find existing chunk files
        chunk_dir = "data/exports/20250414_000000"
        chunk_pattern = os.path.join(chunk_dir, "my_app_tngd_actiontraillinux*.json")
        chunk_files = glob.glob(chunk_pattern)
        
        print(f"2. Found {len(chunk_files)} chunk files in {chunk_dir}")
        
        if not chunk_files:
            print("   ❌ No chunk files found. Run a backup first to generate test data.")
            return False
            
        # Show file sizes
        total_size_mb = 0
        for chunk_file in sorted(chunk_files)[:5]:  # Show first 5 files
            size_mb = os.path.getsize(chunk_file) / (1024 * 1024)
            total_size_mb += size_mb
            print(f"   - {os.path.basename(chunk_file)}: {size_mb:.2f} MB")
        
        if len(chunk_files) > 5:
            for chunk_file in chunk_files[5:]:
                total_size_mb += os.path.getsize(chunk_file) / (1024 * 1024)
            print(f"   - ... and {len(chunk_files) - 5} more files")
            
        print(f"   Total uncompressed size: {total_size_mb:.2f} MB")
        
        # Test compression
        print("3. Testing compression...")
        archive_name = "my_app_tngd_actiontraillinux_test.tar.gz"
        archive_path = os.path.join("data/temp", archive_name)
        
        # Ensure temp directory exists
        os.makedirs("data/temp", exist_ok=True)
        
        # Create archive manually (simulating backup engine workflow)
        print(f"   Creating archive: {archive_path}")
        
        with tarfile.open(archive_path, 'w:gz') as tar:
            for i, chunk_file in enumerate(chunk_files, 1):
                if os.path.exists(chunk_file):
                    # Add file to archive with just the filename (not full path)
                    arcname = os.path.basename(chunk_file)
                    chunk_size_mb = os.path.getsize(chunk_file) / (1024 * 1024)
                    tar.add(chunk_file, arcname=arcname)
                    if i <= 5:  # Show progress for first 5 files
                        print(f"     Added chunk {i}/{len(chunk_files)}: {arcname} ({chunk_size_mb:.2f} MB)")
                    elif i == 6:
                        print(f"     ... adding remaining {len(chunk_files) - 5} files ...")
                else:
                    print(f"     ⚠️ Chunk file not found: {chunk_file}")
        
        # Check archive creation
        if os.path.exists(archive_path):
            archive_size_mb = os.path.getsize(archive_path) / (1024 * 1024)
            compression_ratio = (total_size_mb - archive_size_mb) / total_size_mb * 100
            
            print(f"   ✅ Archive created successfully!")
            print(f"   Archive size: {archive_size_mb:.2f} MB")
            print(f"   Compression ratio: {compression_ratio:.1f}%")
            print(f"   Space saved: {total_size_mb - archive_size_mb:.2f} MB")
            
            # Verify archive contents
            print("4. Verifying archive contents...")
            with tarfile.open(archive_path, 'r:gz') as tar:
                members = tar.getnames()
                print(f"   Archive contains {len(members)} files")
                
                # Show first few files
                for member in sorted(members)[:5]:
                    print(f"     - {member}")
                if len(members) > 5:
                    print(f"     - ... and {len(members) - 5} more files")
                    
            # Test archive path generation (simulating backup engine)
            print("5. Testing OSS path generation...")
            table_name = "my.app.tngd.actiontraillinux"
            backup_run_id = "20250414_000000"
            
            # Simulate the path generation logic from backup engine
            oss_path = f"Devo/2025/04/14/{table_name.replace('.', '_')}.tar.gz"
            print(f"   Generated OSS path: {oss_path}")
            
            print("6. Archive workflow test completed successfully!")
            print(f"   ✅ Archive ready for upload: {archive_path}")
            print(f"   ✅ Target OSS path: {oss_path}")
            
            # Clean up test archive
            print("7. Cleaning up test archive...")
            os.remove(archive_path)
            print(f"   Test archive removed: {archive_path}")
            
            return True
            
        else:
            print("   ❌ Archive creation failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n=== Compression Workflow Test Completed ===")

if __name__ == "__main__":
    test_compression_workflow()
