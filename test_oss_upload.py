#!/usr/bin/env python3
"""
Test script to verify OSS upload functionality
"""

import os
import json
import tempfile
from src.tngd_backup.core.storage_manager import StorageManager
from src.tngd_backup.core.config_manager import Config<PERSON><PERSON><PERSON>

def test_oss_upload():
    """Test OSS upload functionality with a small test file."""
    
    print("=== Testing OSS Upload Functionality ===")
    
    try:
        # Initialize components
        print("1. Initializing configuration and storage manager...")
        config = ConfigManager('config/production.json')
        storage = StorageManager(config)
        
        # Create a test file
        print("2. Creating test file...")
        test_data = {
            "test": "data",
            "timestamp": "2025-07-15",
            "rows": 100,
            "message": "This is a test file for OSS upload verification"
        }
        
        # Create temporary test file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f, indent=2)
            test_file_path = f.name
        
        print(f"   Test file created: {test_file_path}")
        print(f"   File size: {os.path.getsize(test_file_path)} bytes")
        
        # Test OSS connection first
        print("3. Testing OSS connection...")
        success, message = storage.test_connection()
        print(f"   Connection result: {'SUCCESS' if success else 'FAILED'} - {message}")
        
        if not success:
            print("   ❌ OSS connection failed. Cannot proceed with upload test.")
            return False
            
        # Test upload
        print("4. Testing file upload...")
        oss_path = "test/backup_system_test.json"
        
        upload_success, upload_details = storage.upload_file(test_file_path, oss_path)
        
        if upload_success:
            print(f"   ✅ Upload successful!")
            print(f"   OSS path: {oss_path}")
            print(f"   Upload details: {upload_details}")
            
            # Test file info retrieval
            print("5. Verifying uploaded file...")
            file_info = storage.get_object_info(oss_path)
            if file_info:
                print(f"   ✅ File verified in OSS:")
                print(f"   Size: {file_info['size']} bytes")
                print(f"   Last modified: {file_info['last_modified']}")
                print(f"   ETag: {file_info['etag']}")
            else:
                print("   ⚠️ Could not retrieve file info")
                
            # Clean up test file from OSS
            print("6. Cleaning up test file...")
            delete_success = storage.delete_oss_object(oss_path)
            print(f"   Cleanup: {'SUCCESS' if delete_success else 'FAILED'}")
            
        else:
            print(f"   ❌ Upload failed: {upload_details}")
            return False
            
    except Exception as e:
        print(f"   ❌ Test failed with error: {str(e)}")
        return False
        
    finally:
        # Clean up local test file
        if 'test_file_path' in locals() and os.path.exists(test_file_path):
            os.unlink(test_file_path)
            print(f"   Local test file cleaned up: {test_file_path}")
    
    print("\n=== OSS Upload Test Completed Successfully ===")
    return True

if __name__ == "__main__":
    test_oss_upload()
